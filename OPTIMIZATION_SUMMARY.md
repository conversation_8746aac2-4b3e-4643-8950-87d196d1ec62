# 发票识别回写优化总结

## 优化内容

本次优化主要针对 `src/views/advancePaymentBill/index.vue` 文件中的发票识别后回写逻辑进行了以下改进：

### 1. 优化 invoice_type 字段
- **原来**: 未正确使用发票类型
- **现在**: 使用 `item.SubType` 作为发票类型，准确识别发票类型（如 VatSpecialInvoice、VatCommonInvoice 等）

### 2. 优化 title 字段
- **原来**: 使用文件名作为发票标题
- **现在**: 优先使用发票内容，从以下来源提取：
  - `item.Title` 或 `item.Name`
  - 发票明细中的商品名称（`VatInvoiceItemInfos`、`VatElectronicItems`、`GeneralMachineItems`）
  - 如果都没有，才使用文件名

### 3. 添加 origin_file 字段
- **新增**: `originFile` 字段，映射发票原始文件地址

### 4. 添加 invoice_drawer 字段
- **新增**: `invoiceDrawer` 字段，从 `item.Issuer` 或 `item.InvoiceDrawer` 提取开票人信息

### 5. 添加 invoice_review 字段
- **新增**: `invoiceReview` 字段，从 `item.Reviewer` 或 `item.InvoiceReview` 提取复核人信息

### 6. 优化 invoicePrice 计算逻辑
- **原来**: 简单的减法计算
- **现在**: 更智能的计算逻辑：
  - 优先使用 `item.PretaxAmount`
  - 如果没有，使用价税合计减去税额
  - 如果没有税额，直接使用价税合计

## 修改的方法

### 1. `processOcrResults` 方法
- 添加了新的字段映射：`pretaxAmount`、`invoiceTitle`、`invoiceDrawer`、`invoiceReview`

### 2. `extractInvoiceInfo` 方法
- 优化发票代码提取：支持 `item.Code`
- 优化发票内容提取：从多个来源智能提取
- 添加开票人和复核人提取逻辑
- 优化不含税金额计算逻辑

### 3. `saveInvoices` 方法
- 更新保存数据的字段映射
- 添加详细的注释说明每个字段的用途
- 优化字段优先级（如 title 优先使用发票内容）

### 4. `loadExistingInvoices` 方法
- 更新已有发票的字段映射
- 支持新的字段显示

### 5. 界面显示优化
- 已有发票列表：添加开票人、复核人列显示
- OCR识别结果列表：添加发票内容、发票类型、开票人、复核人列显示

## 数据库字段映射

| 前端字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| title | title | 发票内容（优先）或文件名 |
| invoiceUrl | invoiceUrl | 发票图片地址 |
| originFile | originFile | 发票原始文件地址 |
| invoiceCode | invoiceCode | 发票代码 |
| invoiceNo | invoiceNo | 发票号码 |
| invoiceDate | invoiceDate | 开票日期 |
| invoiceType | invoiceType | 发票类型（SubType） |
| invoicePriceTaxSum | invoicePriceTaxSum | 价税合计 |
| invoicePriceTax | invoicePriceTax | 税额 |
| invoicePrice | invoicePrice | 不含税金额 |
| sellerName | sellerName | 销售方名称 |
| buyerName | buyerName | 购买方名称 |
| invoiceDrawer | invoiceDrawer | 开票人 |
| invoiceReview | invoiceReview | 复核人 |

## 优化效果

1. **更准确的发票类型识别**: 使用 SubType 能准确区分专票、普票等类型
2. **更有意义的发票标题**: 显示实际发票内容而不是文件名
3. **完整的发票信息**: 包含开票人、复核人等关键信息
4. **更智能的金额计算**: 多层级的计算逻辑确保数据准确性
5. **更好的用户体验**: 界面显示更多有用信息

## 兼容性

- 保持向后兼容，支持原有的字段名称
- 新增字段为可选，不影响现有功能
- 优化了字段提取的容错性，支持多种数据源
