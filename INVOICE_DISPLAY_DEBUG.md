# 发票列表显示问题调试指南

## 问题现象
已有发票列表不能正确展示

## 可能的原因分析

### 1. API调用问题
- **API参数问题**: `listPrepaymentReimbursementInvoice` 使用GET请求，参数名为 `ids`
- **数据结构问题**: 返回的数据结构可能与期望不符
- **权限问题**: 可能没有权限访问发票数据

### 2. 数据映射问题
- **字段名不匹配**: 后端返回的字段名与前端期望的不一致
- **数据类型问题**: 某些字段的数据类型可能不正确
- **空值处理**: 没有正确处理空值或undefined

### 3. 界面渲染问题
- **条件渲染**: `v-if` 条件可能不满足
- **数据绑定**: 数据绑定可能有问题

## 调试步骤

### 第一步：检查API调用
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 点击发票上传按钮
4. 查看是否有 `listPrepaymentReimbursementInvoice` 的API请求
5. 检查请求参数和响应数据

**期望的API请求**：
```
GET /api/invoice/listPrepaymentReimbursementInvoice?ids=流程ID
```

**期望的响应格式**：
```json
{
  "resultCode": "0",
  "resultMsg": "success",
  "data": [
    {
      "id": "发票ID",
      "title": "发票内容",
      "invoiceUrl": "发票图片地址",
      "invoiceCode": "发票代码",
      "invoiceNo": "发票号码",
      "invoiceDate": "开票日期",
      "invoicePriceTaxSum": "价税合计",
      "sellerName": "销售方名称",
      // ... 其他字段
    }
  ]
}
```

### 第二步：检查控制台日志
查看浏览器控制台是否有以下日志：
- `开始查询发票，processId: xxx`
- `查询发票API返回结果: xxx`
- `原始发票数据: xxx`
- `处理后的发票列表: xxx`

### 第三步：检查数据状态
在浏览器控制台中执行：
```javascript
// 检查Vue实例的数据
$vm0.existingInvoices
$vm0.invoiceLoading
$vm0.currentProcess
```

### 第四步：手动测试API
在控制台中手动调用API：
```javascript
// 假设processId为123
fetch('/api/invoice/listPrepaymentReimbursementInvoice?ids=123')
  .then(res => res.json())
  .then(data => console.log('API响应:', data))
```

## 修复方案

### 方案1：API参数修复
如果API调用失败，可能需要修改参数格式：

```javascript
// 当前实现
listPrepaymentReimbursementInvoice(processId)

// 可能需要的格式
listPrepaymentReimbursementInvoice(processId.toString())
// 或者
listPrepaymentReimbursementInvoice([processId])
```

### 方案2：数据映射修复
如果数据结构不匹配，需要调整映射逻辑：

```javascript
// 检查实际返回的字段名
console.log('实际返回的发票数据:', invoiceData[0])

// 根据实际字段名调整映射
this.existingInvoices = invoiceData.map(invoice => ({
  id: invoice.id || invoice.invoiceId,
  fileName: invoice.title || invoice.invoiceTitle || invoice.fileName,
  // ... 其他字段映射
}))
```

### 方案3：添加更多调试信息
在 `loadExistingInvoices` 方法中添加更详细的日志：

```javascript
loadExistingInvoices(processId) {
  console.log('=== 开始调试发票加载 ===')
  console.log('processId:', processId, typeof processId)
  console.log('currentProcess:', this.currentProcess)
  
  // ... API调用
  
  listPrepaymentReimbursementInvoice(processId).then(res => {
    console.log('=== API响应详情 ===')
    console.log('完整响应:', res)
    console.log('resultCode:', res?.resultCode)
    console.log('data类型:', typeof res?.data)
    console.log('data内容:', res?.data)
    console.log('data长度:', res?.data?.length)
    
    if (res?.data && Array.isArray(res.data)) {
      console.log('第一条发票数据:', res.data[0])
      console.log('发票数据字段:', Object.keys(res.data[0] || {}))
    }
    
    // ... 数据处理
  })
}
```

## 常见问题及解决方案

### 问题1：API返回空数组
**原因**: 流程ID不正确或该流程确实没有发票
**解决**: 检查传入的processId是否正确

### 问题2：API返回错误
**原因**: 权限不足或API路径错误
**解决**: 检查用户权限和API配置

### 问题3：数据映射错误
**原因**: 后端字段名与前端期望不一致
**解决**: 根据实际返回的字段名调整映射逻辑

### 问题4：界面不更新
**原因**: Vue响应式数据更新问题
**解决**: 使用 `this.$set` 或 `this.$forceUpdate()`

## 测试用例

### 测试1：有发票的流程
1. 选择一个已知有发票的流程
2. 点击发票上传
3. 检查是否显示已有发票

### 测试2：无发票的流程
1. 选择一个新创建的流程
2. 点击发票上传
3. 检查是否显示"暂无发票"提示

### 测试3：网络错误
1. 断开网络连接
2. 点击发票上传
3. 检查是否显示错误提示

## 最终验证

修复完成后，确保以下功能正常：
1. ✅ 发票列表正确显示
2. ✅ 发票数量正确显示
3. ✅ 加载状态正确显示
4. ✅ 错误处理正确工作
5. ✅ 编辑功能正常
6. ✅ 删除功能正常
7. ✅ 新增发票后列表更新

## 注意事项

1. **缓存问题**: 清除浏览器缓存后重试
2. **权限问题**: 确保当前用户有查看发票的权限
3. **数据库问题**: 检查数据库中是否确实有发票数据
4. **环境问题**: 确保在正确的环境中测试（开发/测试/生产）
