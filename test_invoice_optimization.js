/**
 * 发票识别优化测试用例
 * 测试新的字段提取和映射逻辑
 */

// 模拟OCR识别返回的数据结构
const mockOcrData = {
  MixedInvoiceItems: [
    {
      SubType: "VatSpecialInvoice", // 增值税专用发票
      TypeDescription: "增值税专用发票",
      SingleInvoiceInfos: {
        VatSpecialInvoice: {
          InvoiceCode: "144031909110",
          Code: "144031909110", // 备用字段
          InvoiceNum: "20190000001",
          Number: "20190000001", // 备用字段
          InvoiceDate: "2019-01-15",
          Date: "2019-01-15", // 备用字段
          TotalAmount: "1130.00",
          Total: "1130.00", // 备用字段
          TotalTax: "130.00",
          Tax: "130.00", // 备用字段
          PretaxAmount: "1000.00",
          SellerName: "北京测试公司",
          Seller: "北京测试公司", // 备用字段
          PurchaserName: "上海采购公司",
          BuyerName: "上海采购公司", // 备用字段
          Buyer: "上海采购公司", // 备用字段
          Title: "技术服务费",
          Name: "技术服务费", // 备用字段
          Issuer: "张三",
          InvoiceDrawer: "张三", // 备用字段
          Reviewer: "李四",
          InvoiceReview: "李四", // 备用字段
          VatInvoiceItemInfos: [
            {
              Name: "技术开发服务",
              TaxRate: "13%"
            },
            {
              Name: "技术咨询服务", 
              TaxRate: "6%"
            }
          ]
        }
      }
    },
    {
      SubType: "VatCommonInvoice", // 增值税普通发票
      TypeDescription: "增值税普通发票",
      SingleInvoiceInfos: {
        VatCommonInvoice: {
          InvoiceCode: "144031909111",
          InvoiceNum: "20190000002",
          InvoiceDate: "2019-01-16",
          TotalAmount: "565.00",
          TotalTax: "65.00",
          PretaxAmount: "500.00",
          SellerName: "深圳销售公司",
          PurchaserName: "广州购买公司",
          // 没有Title，应该从VatElectronicItems提取
          VatElectronicItems: [
            {
              Name: "办公用品"
            },
            {
              Name: "文具用品"
            }
          ],
          Issuer: "王五",
          Reviewer: "赵六"
        }
      }
    },
    {
      SubType: "TrainTicket", // 火车票
      TypeDescription: "火车票",
      SingleInvoiceInfos: {
        TrainTicket: {
          Number: "E123456789",
          Date: "2019-01-17",
          Total: "150.50",
          Fare: "150.50", // 备用字段
          Tax: "13.64",
          // 没有明确的Title，应该使用文件名
          Buyer: "出差人员"
        }
      }
    }
  ]
};

// 模拟提取发票信息的函数（基于优化后的逻辑）
function extractInvoiceInfo(item, fileUrl, invoiceType, fileName, index) {
  if (!item) return null;

  const invoice = {};

  // 发票代码
  if (item.InvoiceCode || item.Code) {
    invoice.invoiceCode = item.InvoiceCode || item.Code;
  }

  // 发票号码
  if (item.InvoiceNum || item.Number || item.ReceiptNumber) {
    invoice.invoiceNumber = item.InvoiceNum || item.Number || item.ReceiptNumber;
  }

  // 开票日期
  if (item.InvoiceDate || item.Date) {
    invoice.invoiceDate = item.InvoiceDate || item.Date;
  }

  // 价税合计
  if (item.TotalAmount || item.Total || item.Fare) {
    invoice.totalAmount = item.TotalAmount || item.Total || item.Fare;
  }

  // 税额
  if (item.TotalTax || item.Tax || item.TaxAmount) {
    invoice.taxAmount = item.TotalTax || item.Tax || item.TaxAmount;
  }

  // 不含税金额 - 优化计算逻辑
  if (item.PretaxAmount) {
    invoice.pretaxAmount = item.PretaxAmount;
  } else if (invoice.totalAmount && invoice.taxAmount) {
    invoice.pretaxAmount = parseFloat(invoice.totalAmount) - parseFloat(invoice.taxAmount);
  } else if (invoice.totalAmount) {
    // 如果没有税额，假设为含税金额
    invoice.pretaxAmount = invoice.totalAmount;
  }

  // 销售方
  if (item.SellerName || item.Seller) {
    invoice.sellerName = item.SellerName || item.Seller;
  }

  // 购买方
  if (item.PurchaserName || item.BuyerName || item.Buyer) {
    invoice.buyerName = item.PurchaserName || item.BuyerName || item.Buyer;
  }

  // 发票内容/抬头 - 优化title字段
  if (item.Title || item.Name) {
    invoice.invoiceTitle = item.Title || item.Name;
  } else if (item.VatInvoiceItemInfos && item.VatInvoiceItemInfos.length > 0) {
    // 从发票明细中提取商品名称作为发票内容
    invoice.invoiceTitle = item.VatInvoiceItemInfos.map(detail => detail.Name).join(',');
  } else if (item.VatElectronicItems && item.VatElectronicItems.length > 0) {
    invoice.invoiceTitle = item.VatElectronicItems.map(detail => detail.Name).join(',');
  } else if (item.GeneralMachineItems && item.GeneralMachineItems.length > 0) {
    invoice.invoiceTitle = item.GeneralMachineItems.map(detail => detail.Name).join(',');
  }

  // 开票人
  if (item.Issuer || item.InvoiceDrawer) {
    invoice.invoiceDrawer = item.Issuer || item.InvoiceDrawer;
  }

  // 复核人
  if (item.Reviewer || item.InvoiceReview) {
    invoice.invoiceReview = item.Reviewer || item.InvoiceReview;
  }

  return invoice;
}

// 模拟处理OCR结果的函数
function processOcrResults(ocrData, fileUrl, fileName) {
  const results = [];
  if (ocrData.MixedInvoiceItems) {
    ocrData.MixedInvoiceItems.forEach((item, index) => {
      const invoice = extractInvoiceInfo(item.SingleInvoiceInfos[item.SubType], fileUrl, item.SubType, fileName, index);
      if (invoice) {
        results.push({
          fileName: fileName,
          imageUrl: fileUrl,
          invoiceCode: invoice.invoiceCode || '',
          invoiceNumber: invoice.invoiceNumber || '',
          totalAmount: invoice.totalAmount || '',
          invoiceDate: invoice.invoiceDate || '',
          sellerName: invoice.sellerName || '',
          buyerName: invoice.buyerName || '',
          taxAmount: invoice.taxAmount || '',
          pretaxAmount: invoice.pretaxAmount || '',
          invoiceType: item.SubType || '', // 使用SubType作为发票类型
          invoiceTypeName: item.TypeDescription || '',
          invoiceTitle: invoice.invoiceTitle || '', // 发票内容/抬头
          invoiceDrawer: invoice.invoiceDrawer || '', // 开票人
          invoiceReview: invoice.invoiceReview || '', // 复核人
          originalData: item
        });
      }
    });
  }
  return results;
}

// 运行测试
console.log('=== 发票识别优化测试 ===');
const testResults = processOcrResults(mockOcrData, 'http://example.com/invoice.jpg', 'test_invoice.jpg');

testResults.forEach((result, index) => {
  console.log(`\n--- 发票 ${index + 1} ---`);
  console.log('发票类型:', result.invoiceType, '(' + result.invoiceTypeName + ')');
  console.log('发票内容:', result.invoiceTitle || '未识别');
  console.log('发票代码:', result.invoiceCode || '无');
  console.log('发票号码:', result.invoiceNumber || '无');
  console.log('开票日期:', result.invoiceDate || '无');
  console.log('价税合计:', result.totalAmount || '0');
  console.log('税额:', result.taxAmount || '0');
  console.log('不含税金额:', result.pretaxAmount || '0');
  console.log('销售方:', result.sellerName || '无');
  console.log('购买方:', result.buyerName || '无');
  console.log('开票人:', result.invoiceDrawer || '无');
  console.log('复核人:', result.invoiceReview || '无');
});

console.log('\n=== 测试完成 ===');
console.log('共识别发票数量:', testResults.length);

// 验证优化效果
const optimizationTests = [
  {
    name: '发票类型使用SubType',
    test: () => testResults.every(r => r.invoiceType && r.invoiceType !== ''),
    expected: true
  },
  {
    name: '发票内容优先使用Title',
    test: () => testResults[0].invoiceTitle === '技术服务费',
    expected: true
  },
  {
    name: '发票内容从明细提取',
    test: () => testResults[1].invoiceTitle === '办公用品,文具用品',
    expected: true
  },
  {
    name: '开票人信息提取',
    test: () => testResults[0].invoiceDrawer === '张三',
    expected: true
  },
  {
    name: '复核人信息提取',
    test: () => testResults[0].invoiceReview === '李四',
    expected: true
  },
  {
    name: '不含税金额计算',
    test: () => parseFloat(testResults[0].pretaxAmount) === 1000.00,
    expected: true
  }
];

console.log('\n=== 优化效果验证 ===');
optimizationTests.forEach(test => {
  const result = test.test();
  const status = result === test.expected ? '✅ 通过' : '❌ 失败';
  console.log(`${status} ${test.name}`);
});
