# 发票编辑功能不含税金额显示问题修复

## 问题描述

在发票编辑功能中，不含税金额字段不能正确显示在编辑对话框中。这个问题影响了用户编辑发票时的体验，用户无法看到或修改不含税金额。

## 问题分析

### 根本原因

1. **字段映射缺失**：在 `loadExistingInvoices` 方法中，缺少了 `pretaxAmount`（不含税金额）字段的映射
2. **数值格式问题**：从后端返回的数值可能包含格式化字符（如千分位分隔符、货币符号）或者是字符串类型，导致在输入框中显示异常
3. **数据类型不一致**：不同来源的数据（OCR识别、数据库存储）可能有不同的数据类型和格式

### 具体问题

#### 1. 字段映射缺失
在 `loadExistingInvoices` 方法中，原来的代码缺少了不含税金额的映射：

```javascript
// 原始代码 - 缺少 pretaxAmount 字段
this.existingInvoices = invoiceData.map(invoice => ({
  id: invoice.id,
  fileName: invoice.title || invoice.fileName || '发票文件',
  // ... 其他字段
  taxAmount: invoice.invoicePriceTax || invoice.taxAmount || '',
  // 缺少 pretaxAmount 字段映射
  invoiceDrawer: invoice.invoiceDrawer || '',
  // ...
}))
```

#### 2. 数值格式问题
编辑表单中直接使用原始数值，可能导致显示问题：

```javascript
// 原始代码 - 直接使用原始值
invoicePrice: invoice.pretaxAmount || '',  // 可能是 "1,234.56" 或 "￥1234.56"
```

## 解决方案

### 1. 添加字段映射

在 `loadExistingInvoices` 方法中添加了 `pretaxAmount` 字段的映射：

```javascript
this.existingInvoices = invoiceData.map(invoice => ({
  id: invoice.id,
  fileName: invoice.title || invoice.fileName || '发票文件',
  imageUrl: invoice.invoiceUrl || invoice.originFile || invoice.imageUrl,
  invoiceCode: invoice.invoiceCode || '',
  invoiceNumber: invoice.invoiceNo || invoice.invoiceNumber || '',
  totalAmount: invoice.invoicePriceTaxSum || invoice.totalAmount || '',
  invoiceDate: invoice.invoiceDate || '',
  sellerName: invoice.sellerName || '',
  buyerName: invoice.buyerName || '',
  taxAmount: invoice.invoicePriceTax || invoice.taxAmount || '',
  pretaxAmount: invoice.invoicePrice || invoice.pretaxAmount || '', // ✅ 添加不含税金额映射
  invoiceDrawer: invoice.invoiceDrawer || '',
  invoiceReview: invoice.invoiceReview || '',
  invoiceType: invoice.invoiceType || '',
  originalData: invoice
}))
```

### 2. 添加数值格式化方法

创建了 `formatNumberForInput` 方法来处理各种数值格式：

```javascript
// 格式化数值以适配输入框显示
formatNumberForInput(value) {
  if (!value && value !== 0) return ''
  
  try {
    // 如果是字符串，先转换为数字
    let numValue = value
    if (typeof value === 'string') {
      // 移除可能的千分位分隔符和货币符号
      numValue = value.replace(/[,￥$]/g, '')
      numValue = parseFloat(numValue)
    }
    
    // 检查是否为有效数字
    if (isNaN(numValue)) {
      console.warn('无效的数值:', value)
      return ''
    }
    
    // 保留两位小数并转换为字符串
    return numValue.toFixed(2)
  } catch (error) {
    console.error('数值格式化失败:', error, value)
    return ''
  }
}
```

### 3. 在编辑方法中使用格式化

在 `editExistingInvoice` 和 `editOcrResult` 方法中使用数值格式化：

```javascript
// 编辑已有发票
editExistingInvoice(invoice) {
  this.editInvoiceForm = {
    id: invoice.id,
    title: invoice.fileName || '',
    invoiceCode: invoice.invoiceCode || '',
    invoiceNo: invoice.invoiceNumber || '',
    invoiceDate: this.formatDateForPicker(invoice.invoiceDate),
    invoiceType: invoice.invoiceType || '',
    invoicePriceTaxSum: this.formatNumberForInput(invoice.totalAmount), // ✅ 格式化
    invoicePriceTax: this.formatNumberForInput(invoice.taxAmount),       // ✅ 格式化
    invoicePrice: this.formatNumberForInput(invoice.pretaxAmount),       // ✅ 格式化
    sellerName: invoice.sellerName || '',
    buyerName: invoice.buyerName || '',
    invoiceDrawer: invoice.invoiceDrawer || '',
    invoiceReview: invoice.invoiceReview || ''
  }
  
  this.editInvoiceDialogVisible = true
}

// 编辑OCR识别结果
editOcrResult(row) {
  this.editInvoiceForm = {
    id: '',
    title: row.invoiceTitle || row.fileName || '',
    invoiceCode: row.invoiceCode || '',
    invoiceNo: row.invoiceNumber || '',
    invoiceDate: this.formatDateForPicker(row.invoiceDate),
    invoiceType: row.invoiceType || '',
    invoicePriceTaxSum: this.formatNumberForInput(row.totalAmount),  // ✅ 格式化
    invoicePriceTax: this.formatNumberForInput(row.taxAmount),       // ✅ 格式化
    invoicePrice: this.formatNumberForInput(row.pretaxAmount),       // ✅ 格式化
    sellerName: row.sellerName || '',
    buyerName: row.buyerName || '',
    invoiceDrawer: row.invoiceDrawer || '',
    invoiceReview: row.invoiceReview || ''
  }
  
  this.editInvoiceDialogVisible = true
}
```

## 支持的数值格式

### 输入格式支持

| 格式类型 | 示例 | 处理结果 |
|---------|------|----------|
| 纯数字 | `1234.56` | `1234.56` |
| 千分位格式 | `1,234.56` | `1234.56` |
| 货币格式 | `￥1,234.56` | `1234.56` |
| 美元格式 | `$1,234.56` | `1234.56` |
| 字符串数字 | `"1234.56"` | `1234.56` |
| 整数 | `1234` | `1234.00` |
| 零值 | `0` | `0.00` |
| 空值 | `null`, `undefined`, `""` | `""` |

### 输出格式

统一输出为保留两位小数的字符串格式，例如：
- `1234.56` → `"1234.56"`
- `1234` → `"1234.00"`
- `0` → `"0.00"`

## 错误处理

### 1. 数值验证

```javascript
// 检查是否为有效数字
if (isNaN(numValue)) {
  console.warn('无效的数值:', value)
  return ''
}
```

### 2. 异常捕获

```javascript
try {
  // 数值处理逻辑
} catch (error) {
  console.error('数值格式化失败:', error, value)
  return ''
}
```

### 3. 空值处理

```javascript
if (!value && value !== 0) return ''
```

## 测试用例

### 测试数据

```javascript
const testValues = [
  1234.56,          // 数字 → "1234.56"
  "1234.56",        // 字符串数字 → "1234.56"
  "1,234.56",       // 千分位 → "1234.56"
  "￥1,234.56",     // 人民币 → "1234.56"
  "$1,234.56",      // 美元 → "1234.56"
  1234,             // 整数 → "1234.00"
  0,                // 零 → "0.00"
  "",               // 空字符串 → ""
  null,             // null → ""
  undefined,        // undefined → ""
  "invalid",        // 无效值 → ""
]
```

### 预期结果

所有有效的数值都应该转换为保留两位小数的字符串格式，无效值返回空字符串。

## 修复效果

### 修复前

1. **不含税金额字段为空**：编辑对话框中不含税金额字段始终为空
2. **数值显示异常**：可能显示带格式的字符串，如 "￥1,234.56"
3. **用户体验差**：无法正确编辑不含税金额

### 修复后

1. **正确显示数值**：不含税金额字段能够正确显示数值
2. **格式统一**：所有金额字段都使用统一的数值格式（保留两位小数）
3. **编辑体验好**：用户可以正常查看和编辑不含税金额
4. **数据一致性**：确保编辑后的数据格式正确

## 兼容性

### 数据兼容性

- 兼容现有数据库中的各种数值格式
- 兼容OCR识别返回的多种数值格式
- 向后兼容，不影响现有功能

### 浏览器兼容性

- 使用标准的JavaScript数值处理方法
- 兼容主流浏览器
- 无需额外的polyfill

## 总结

通过添加字段映射和数值格式化方法，我们成功解决了发票编辑功能中不含税金额不能正确显示的问题。该方案：

1. **完整性**：确保所有金额字段都能正确映射和显示
2. **健壮性**：包含完善的错误处理和数值验证
3. **一致性**：统一的数值格式化处理
4. **易维护**：代码结构清晰，易于扩展
5. **用户友好**：提供良好的编辑体验

现在用户在编辑发票时，不含税金额字段能够正确显示，并且可以正常编辑和保存。
