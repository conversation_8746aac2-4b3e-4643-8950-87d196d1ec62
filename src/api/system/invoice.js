import request from "../../utils/request";

const BASE_REQUEST = '/api/invoice'
const BASE_REQUEST_TYPE = '/api/invoiceType'

export function getList(data) {
  return request({
    url: `${BASE_REQUEST}/list`,
    method: 'post',
    data
  })
}
export function updateBilMonth(data) {
  return request({
    url: `${BASE_REQUEST}/updateBilMonth`,
    method: 'post',
    data
  })
}
export function getStatistical(data) {
  return request({
    url: `${BASE_REQUEST}/statistical`,
    method: 'post',
    data
  })
}
export function invoiceListBySales(data) {
  return request({
    url: `${BASE_REQUEST}/invoiceListBySales`,
    method: 'post',
    data
  })
}
export function invoiceListBySalesAccount(data) {
  return request({
    url: `${BASE_REQUEST}/invoiceListBySalesAccount`,
    method: 'post',
    data
  })
}

export function listBySelf(data) {
  return request({
    url: `${BASE_REQUEST}/listBySelf`,
    method: 'post',
    data
  })
}

export function invoiceTypes(model='type') {
  return request({
    url: `${BASE_REQUEST_TYPE}`,
    method: 'get',
    params: {
      model
    }
  })
}

export function updateBilMonthSlef(data) {
  return request({
    url: `${BASE_REQUEST}/updateBilMonthSlef`,
    method: 'post',
    data
  })
}

export function uploadSalesInvoice(data) {
  return request({
    url: `${BASE_REQUEST}/uploadSalesInvoice`,
    method: 'post',
    data
  })
}


export function uploadPurchaseInvoice(data) {
  return request({
    url: `${BASE_REQUEST}/uploadPurchaseInvoice`,
    method: 'post',
    data
  })
}

export function uploadReceiptInvoice(data) {
  return request({
    url: `${BASE_REQUEST}/uploadReceiptInvoice`,
    method: 'post',
    data
  })
}

export function delInvoiceDialog(data) {
  return request({
    url: `${BASE_REQUEST}/delInvoiceDialog`,
    method: 'post',
    data
  })
}
export function getInvoiceCountByProcessIds(ids){
  return request({
    url: `${BASE_REQUEST}/getInvoiceCountByProcessIds`,
    method: 'get',
    params: {ids}
  })
}
export function listPrepaymentReimbursementInvoice(ids){
  return request({
    url: `${BASE_REQUEST}/listPrepaymentReimbursementInvoice`,
    method: 'get',
    params: {ids}
  })
}

//  @TableId
//     @ApiModelProperty(value = "id")
//     private String id;

//     @NotBlank
//     @ApiModelProperty(value = "发票号")
//     private String invoiceNo;

//     @ApiModelProperty(value = "发票代码")
//     private String invoiceCode;

//     @NotBlank
//     @ApiModelProperty(value = "发票日期")
//     private String invoiceDate;

//     @NotNull
//     @ApiModelProperty(value = "发票不含税金额")
//     private BigDecimal invoicePrice;

//     @NotNull
//     @ApiModelProperty(value = "发票税额")
//     private BigDecimal invoicePriceTax;

//     @NotNull
//     @ApiModelProperty(value = "发票含税金额")
//     private BigDecimal invoicePriceTaxSum;

//     @NotBlank
//     @ApiModelProperty(value = "税率")
//     private String invoiceTax;

//     @NotBlank
//     @ApiModelProperty(value = "买方名称")
//     private String buyerName;

//     @ApiModelProperty(value = "买方纳税人识别号")
//     private String buyerTaxpayerNo;

//     @ApiModelProperty(value = "买方地址电话")
//     private String buyerAddressTel;

//     @ApiModelProperty(value = "买方开户行及账号")
//     private String buyerAccount;

//     @NotBlank
//     @ApiModelProperty(value = "卖方名称")
//     private String sellerName;

//     @ApiModelProperty(value = "卖方纳税人识别号")
//     private String sellerTaxpayerNo;

//     @ApiModelProperty(value = "卖方地址电话")
//     private String sellerAddressTel;

//     @ApiModelProperty(value = "卖方开户行及账号")
//     private String sellerAccount;

//     @ApiModelProperty(value = "发票图片")
//     private String originFile;

//     @ApiModelProperty(value = "收款人")
//     private String invoicePayee;

//     @ApiModelProperty(value = "开票人")
//     private String invoiceDrawer;

//     @ApiModelProperty(value = "复核")
//     private String invoiceReview;

//     @ApiModelProperty(value = "发票备注")
//     private String invoiceRemark;

//     @ApiModelProperty(value = "创建时间")
//     private Timestamp createTime;

//     @ApiModelProperty(value = "修改时间")
//     private Timestamp updateTime;

//     @ApiModelProperty(value = "记账月份")
//     private String recordAccountMonth;

//     @NotNull
//     @ApiModelProperty(value = "状态 0 待提交审批 1 审批中 2待入账 3已完成")
//     private Integer status;

//     @ApiModelProperty(value = "购买方ID")
//     private String buyerId;

//     @ApiModelProperty(value = "销售方ID")
//     private String sellerId;

//     @ApiModelProperty(value = "费用类型ID")
//     private String costTypeId;

//     @ApiModelProperty(value = "费用类型名称")
//     private String costTypeName;

//     @ApiModelProperty(value = "费用字典key")
//     private String costDictKey;

//     @ApiModelProperty(value = "服务名称")
//     private String serviceName;

//     @ApiModelProperty(value = "拆分人")
//     private String splitUserId;

//     @ApiModelProperty(value = "拆分时间")
//     private Timestamp splitTime;

//     @ApiModelProperty(value = "发票类型 普票、专票")
//     private String invoiceType;

//     @ApiModelProperty(value = "销项发票、进项发票")
//     private String outInputInvoice;

//     @ApiModelProperty(value = "是否重复")
//     private Integer isItRepeated;

//     @ApiModelProperty(value = "数据来源")
//     private String invoiceDataSource;

//     @ApiModelProperty(value = "关联流程id")
//     private String associatedProcessId;

//     @ApiModelProperty(value = "是否生成凭证")
//     private Integer isCertificate;

//     @ApiModelProperty(value = "凭证id")
//     private String credentialId;
//     @ApiModelProperty(value = "是否删除")
//     private Integer delFlag;

//     @ApiModelProperty(value = "发票名称")
//     private String title;
export function addPrepaymentReimbursementInvoice(list){
  return request({
    url: `${BASE_REQUEST}/addPrepaymentReimbursementInvoice`,
    method: 'post',
    data: list
  })
}

export function updatePrepaymentReimbursementInvoice(data){
  return request({
    url: `${BASE_REQUEST}/updatePrepaymentReimbursementInvoice`,
    method: 'post',
    data: data
  })
}
